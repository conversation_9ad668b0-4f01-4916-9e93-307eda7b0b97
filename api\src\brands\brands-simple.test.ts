import { Test, TestingModule } from '@nestjs/testing';
import { BrandService } from './brands.service';
import { BrandController } from './brands.controller';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Brand } from './entities/brand.entity';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { CreateBrandDto } from './dto/create-brand.dto';
import { ConflictException } from '@nestjs/common';

describe('Brand Module Tests', () => {
	describe('BrandService', () => {
		let service: BrandService;
		let mockRepository: any;

		beforeEach(async () => {
			mockRepository = {
				findOne: jest.fn(),
				save: jest.fn(),
				create: jest.fn(),
				find: jest.fn(),
				findAndCount: jest.fn()
			};

			const module: TestingModule = await Test.createTestingModule({
				providers: [
					BrandService,
					{
						provide: getRepositoryToken(Brand),
						useValue: mockRepository
					},
					{
						provide: WinstonLogger,
						useValue: {
							log: jest.fn(),
							error: jest.fn()
						}
					}
				]
			}).compile();

			service = module.get<BrandService>(BrandService);
		});

		afterEach(() => {
			jest.clearAllMocks();
		});

		it('should be defined', () => {
			expect(service).toBeDefined();
		});

		describe('createBrand', () => {
			it('should create a brand successfully', async () => {
				const createBrandDto: CreateBrandDto = { name: 'Test Brand' };
				const mockBrand = {
					id: '1',
					name: 'Test Brand',
					slug: 'testbrand'
				};

				mockRepository.findOne.mockResolvedValue(null);
				mockRepository.create.mockReturnValue(mockBrand);
				mockRepository.save.mockResolvedValue(mockBrand);

				const result = await service.createBrand(createBrandDto);

				expect(mockRepository.findOne).toHaveBeenCalledWith({
					where: { name: createBrandDto.name }
				});
				expect(mockRepository.create).toHaveBeenCalledWith(createBrandDto);
				expect(mockRepository.save).toHaveBeenCalledWith(mockBrand);
				expect(result).toEqual(mockBrand);
			});

			it('should throw ConflictException if brand already exists', async () => {
				const createBrandDto: CreateBrandDto = { name: 'Existing Brand' };
				const existingBrand = { id: '1', name: 'Existing Brand' };

				mockRepository.findOne.mockResolvedValue(existingBrand);

				await expect(service.createBrand(createBrandDto)).rejects.toThrow(
					ConflictException
				);
				expect(mockRepository.create).not.toHaveBeenCalled();
				expect(mockRepository.save).not.toHaveBeenCalled();
			});
		});

		describe('getAllBrands', () => {
			it('should return paginated brands', async () => {
				const mockBrands = [{ id: '1', name: 'Test Brand' }];
				mockRepository.findAndCount.mockResolvedValue([mockBrands, 1]);

				const result = await service.getAllBrands(1, 10, '', 'DESC');

				expect(mockRepository.findAndCount).toHaveBeenCalledWith({
					where: {},
					skip: 0,
					take: 10,
					order: { createdAt: 'DESC' }
				});
				expect(result).toEqual({ brands: mockBrands, total: 1 });
			});
		});

		describe('getBrandById', () => {
			it('should return a brand by id', async () => {
				const mockBrand = { id: '1', name: 'Test Brand' };
				mockRepository.findOne.mockResolvedValue(mockBrand);

				const result = await service.getBrandById('1');

				expect(mockRepository.findOne).toHaveBeenCalledWith({
					where: { id: '1' }
				});
				expect(result).toEqual(mockBrand);
			});

			it('should return null if brand not found', async () => {
				mockRepository.findOne.mockResolvedValue(null);

				const result = await service.getBrandById('nonexistent');

				expect(result).toBeNull();
			});
		});
	});

	describe('BrandController', () => {
		let controller: BrandController;
		let service: any;

		beforeEach(async () => {
			const mockService = {
				createBrand: jest.fn(),
				getAllBrands: jest.fn(),
				getBrandById: jest.fn(),
				getBrandBySlug: jest.fn()
			};

			const module: TestingModule = await Test.createTestingModule({
				controllers: [BrandController],
				providers: [
					{
						provide: BrandService,
						useValue: mockService
					},
					{
						provide: WinstonLogger,
						useValue: {
							log: jest.fn(),
							error: jest.fn()
						}
					}
				]
			}).compile();

			controller = module.get<BrandController>(BrandController);
			service = module.get<BrandService>(BrandService);
		});

		afterEach(() => {
			jest.clearAllMocks();
		});

		it('should be defined', () => {
			expect(controller).toBeDefined();
		});

		describe('create', () => {
			it('should create a brand', async () => {
				const createBrandDto: CreateBrandDto = { name: 'Test Brand' };
				const mockBrand = { id: '1', name: 'Test Brand' };

				service.createBrand.mockResolvedValue(mockBrand);

				const result = await controller.create(createBrandDto);

				expect(service.createBrand).toHaveBeenCalledWith(createBrandDto);
				expect(result).toEqual(mockBrand);
			});
		});

		describe('findbyId', () => {
			it('should return a brand by id', async () => {
				const mockBrand = { id: '1', name: 'Test Brand' };
				service.getBrandById.mockResolvedValue(mockBrand);

				const result = await controller.findbyId('1');

				expect(service.getBrandById).toHaveBeenCalledWith('1');
				expect(result).toEqual(mockBrand);
			});
		});

		describe('findAll', () => {
			it('should return paginated brands', async () => {
				const mockResult = { brands: [{ id: '1', name: 'Test Brand' }], total: 1 };
				service.getAllBrands.mockResolvedValue(mockResult);

				const result = await controller.findAll();

				expect(service.getAllBrands).toHaveBeenCalledWith(1, 10, '', 'DESC');
				expect(result).toEqual(mockResult);
			});
		});

		describe('findBySlug', () => {
			it('should return a brand by slug', async () => {
				const mockBrand = { id: '1', name: 'Test Brand', slug: 'test-brand' };
				service.getBrandBySlug.mockResolvedValue(mockBrand);

				const result = await controller.findBySlug('test-brand');

				expect(service.getBrandBySlug).toHaveBeenCalledWith('test-brand');
				expect(result).toEqual(mockBrand);
			});
		});
	});
});
