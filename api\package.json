{"name": "Nidana Backend API", "version": "1.0.1", "description": "API description", "author": "<PERSON><PERSON><PERSON>", "private": true, "license": "UNLICENSED", "tag": "", "scripts": {"build": "node --max-old-space-size=4096 ./node_modules/.bin/nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "load-secrets": "ts-node scripts/load-secrets.ts", "build:env": "npm run load-secrets", "start:dev": "nest start --watch", "start:": "nest start --watch", "wsstart": "node build/src/cron.js", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "gts lint", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "coverage:bitbucket": "NODE_ENV=ci nyc --reporter=lcov ./node_modules/.bin/mocha --recursive --exit --config ./test/.mocharc.js", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prepare": "npm run compile", "clean": "gts clean", "compile": "NODE_OPTIONS='--max-old-space-size=4096' tsc --incremental", "fix": "gts fix", "pretest": "npm run compile", "posttest": "", "typeorm": "ts-node ./node_modules/typeorm/cli", "migration:run": "npm run typeorm migration:run -- -d ./src/database/data-source.ts", "migration:generate": "npm run typeorm migration:generate -- -d ./src/database/data-source.ts ./src/migrations/$npm_config_name", "migration:create": "npm run typeorm migration:create -- ./src/migrations/$npm_config_name", "migration:revert": "npm run typeorm migration:revert -- -d ./src/database/data-source.ts", "migration:show": "npm run typeorm migration:show -- -d ./src/database/data-source.ts", "seed": "ts-node ./src/seeders/clinic_rooms.seeder.ts", "seed:all": "ts-node ./src/seeders/master-seeder.ts", "coverage": "nyc npm run test:cov", "release": "standard-version", "sqs:create:dlq": "NODE_ENV=${npm_config_env} ts-node -r tsconfig-paths/register ./src/utils/aws/sqs/create-dead-letter-queue.ts", "sqs:create": "NODE_ENV=${npm_config_env} ts-node -r tsconfig-paths/register ./src/utils/aws/sqs/create-queues.ts"}, "dependencies": {"@aws-sdk/client-ec2": "^3.670.0", "@aws-sdk/client-elastic-load-balancing-v2": "^3.670.0", "@aws-sdk/client-s3": "^3.600.0", "@aws-sdk/client-secrets-manager": "^3.600.0", "@aws-sdk/client-sqs": "^3.600.0", "@aws-sdk/credential-providers": "^3.600.0", "@nestjs/axios": "^3.1.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.2", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.4.4", "@nestjs/schedule": "^4.1.0", "@nestjs/swagger": "^7.3.1", "@nestjs/terminus": "^10.2.3", "@nestjs/typeorm": "^10.0.2", "@nestjs/websockets": "^10.4.4", "@tanstack/react-table": "^8.20.5", "@types/decimal.js": "^0.0.32", "@types/geoip-lite": "^1.4.4", "@types/nodemailer": "^6.4.15", "aws-sdk": "^2.1691.0", "axios": "^1.7.7", "axios-cookiejar-support": "^4.0.7", "bcrypt": "^5.1.1", "chai": "^4.4.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cls-hooked": "^4.2.2", "compress-pdf": "^0.5.1", "config": "^3.3.11", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "decimal.js": "^10.5.0", "folktale": "^2.3.2", "geoip-lite": "^1.4.10", "helmet": "^7.1.0", "http-cookie-agent": "^7.0.1", "ioredis": "^5.4.1", "lucide-react": "^0.438.0", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "nestjs-pino": "^4.1.0", "newrelic": "^12.9.0", "node-mocks-http": "^1.15.0", "nodemailer": "^6.9.14", "nodemailer-mock": "^2.0.6", "openai": "^4.73.1", "passport-jwt": "^4.0.1", "pdf-lib": "^1.17.1", "pg": "^8.12.0", "pino": "^9.1.0", "pino-multi-stream": "^6.0.0", "puppeteer": "^23.4.0", "rate-limiter-flexible": "^5.0.3", "redis": "^4.7.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "sinon": "^18.0.0", "sinon-chai": "^3.7.0", "slugify": "^1.6.6", "socket.io": "^4.8.0", "socket.io-client": "^4.8.0", "tough-cookie": "^4.1.4", "typeorm": "^0.3.20", "uuid": "^10.0.0", "uuidv7": "^1.0.1", "winston": "^3.13.0", "winston-cloudwatch": "^6.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.2", "@types/cls-hooked": "^4.3.8", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/express": "^4.17.17", "@types/jest": "^29.5.14", "@types/multer": "^1.4.11", "@types/newrelic": "^9.14.4", "@types/node": "20.12.7", "@types/passport": "^1.0.16", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.0", "@types/tough-cookie": "^4.0.5", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "gts": "^5.3.1", "husky": "^9.0.11", "jest": "^29.7.0", "pino-pretty": "^11.2.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "sqs-consumer": "^11.4.0", "standard-version": "^9.5.0", "supertest": "^6.3.3", "ts-jest": "^29.2.2", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.4.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": ["ts-jest", {"allowJs": true}]}, "collectCoverageFrom": ["**/*.(t|j)s", "!src/utils/**", "!src/health/**"], "coverageDirectory": "../coverage", "testEnvironment": "node", "coveragePathIgnorePatterns": ["/dto/.*\\.dto\\.ts$", ".strategy", ".config", "main.ts", "app.controller.ts", "/entities", ".module.ts", "/migrations", "/src/utils", "/src/app-versioning", "/seeders", ".guard", ".dto.spec.ts", "src/health", "src/utils/", "src/data-migration", "src/socket", "src/cron.ts", "src/app.service.ts", "src/database/", "src/payment-details", "src/clinic_integrations", "src/patient-global-reminders", "src/analytics"], "testPathIgnorePatterns": ["/node_modules/", "/dto/.*\\.dto\\.ts$", "src/health", "src/utils"], "moduleNameMapper": {"^newrelic$": "<rootDir>/utils/new-relic/__mocks__/newrelic.js"}}, "nyc": {"extends": "@istanbuljs/nyc-config-typescript", "all": true, "include": ["src/**/*.ts"], "exclude": ["test", "**/*.spec.ts", "src/main.ts", "src/health/**", "src/utils/**"], "reporter": ["html", "text", "text-summary"], "extension": [".ts"]}}