import { Test, TestingModule } from '@nestjs/testing';
import { BrandController } from './brands.controller';
import { BrandService } from './brands.service';
import { CreateBrandDto } from './dto/create-brand.dto';
import { BrandWithSettingsDto } from './dto/brand-with-settings.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { HttpException, HttpStatus } from '@nestjs/common';
import { Brand } from './entities/brand.entity';
import { User } from '../users/entities/user.entity';
import { RoleService } from '../roles/role.service';

describe('BrandController', () => {
	let controller: BrandController;
	let brandService: jest.Mocked<BrandService>;
	let logger: jest.Mocked<WinstonLogger>;

	const mockBrandService = {
		createBrand: jest.fn(),
		getAllBrands: jest.fn(),
		getBrandById: jest.fn(),
		getBrandBySlug: jest.fn()
	};

	const mockBrand: Brand = {
		id: '1',
		name: 'Test Brand',
		slug: 'testbrand',
		createdAt: new Date('2023-01-01'),
		updatedAt: new Date('2023-01-01'),
		createdBy: 'user-1',
		createdByUser: new User(),
		updatedBy: 'user-1',
		updatedByUser: new User(),
		clinics: [],
		generateSlug: function (): void {
			this.slug = this.name.toLowerCase().replace(/\s+/g, '');
		}
	};

	const mockBrandWithSettings: BrandWithSettingsDto = {
		id: '1',
		name: 'Test Brand',
		slug: 'testbrand',
		createdAt: new Date('2023-01-01'),
		updatedAt: new Date('2023-01-01'),
		createdBy: 'user-1',
		updatedBy: 'user-1',
		hasClientBookingEnabled: true,
		clinics: [
			{
				id: 'clinic-1',
				name: 'Test Clinic',
				phoneNumbers: [{ country_code: '+1', number: '**********' }]
			}
		]
	};

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [BrandController],
			providers: [
				{
					provide: BrandService,
					useValue: mockBrandService
				},
				{
					provide: RoleService,
					useValue: {
						findByName: jest.fn(),
						findById: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: { log: jest.fn(), error: jest.fn() }
				}
			]
		}).compile();

		controller = module.get<BrandController>(BrandController);
		brandService = module.get<BrandService>(
			BrandService
		) as jest.Mocked<BrandService>;
		logger = module.get<WinstonLogger>(WinstonLogger) as jest.Mocked<WinstonLogger>;
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('create', () => {
		const mockCreateBrandDto: CreateBrandDto = { name: 'New Brand' };

		it('should create a new brand successfully', async () => {
			mockBrandService.createBrand.mockResolvedValue(mockBrand);

			const result = await controller.create(mockCreateBrandDto);

			expect(result).toEqual(mockBrand);
			expect(brandService.createBrand).toHaveBeenCalledWith(
				mockCreateBrandDto
			);
		});

		it('should throw an error if creating a brand fails', async () => {
			const error = new HttpException(
				'Brand creation failed',
				HttpStatus.BAD_REQUEST
			);
			mockBrandService.createBrand.mockRejectedValue(error);

			await expect(controller.create(mockCreateBrandDto)).rejects.toThrow(
				'Brand creation failed'
			);
			expect(brandService.createBrand).toHaveBeenCalledWith(
				mockCreateBrandDto
			);
		});
	});

	describe('findById', () => {
		it('should return a brand by id', async () => {
			const brandId = '1';
			mockBrandService.getBrandById.mockResolvedValue(mockBrand);

			const result = await controller.findbyId(brandId);

			expect(result).toEqual(mockBrand);
			expect(brandService.getBrandById).toHaveBeenCalledWith(brandId);
		});

		it('should throw an error if the brand is not found', async () => {
			const brandId = '2';
			const error = new HttpException(
				'Brand not found',
				HttpStatus.NOT_FOUND
			);
			mockBrandService.getBrandById.mockRejectedValue(error);

			await expect(controller.findbyId(brandId)).rejects.toThrow(
				'Brand not found'
			);
			expect(brandService.getBrandById).toHaveBeenCalledWith(brandId);
		});
	});

	describe('findAll', () => {
		it('should return paginated brands with default parameters', async () => {
			const mockResult = { brands: [mockBrand], total: 1 };
			mockBrandService.getAllBrands.mockResolvedValue(mockResult);

			const result = await controller.findAll();

			expect(logger.log).toHaveBeenCalledWith('Fetching all brands', {
				page: 1,
				limit: 10,
				orderBy: 'DESC'
			});
			expect(brandService.getAllBrands).toHaveBeenCalledWith(1, 10, '', 'DESC');
			expect(result).toEqual(mockResult);
		});

		it('should return paginated brands with custom parameters', async () => {
			const mockResult = { brands: [mockBrand], total: 1 };
			mockBrandService.getAllBrands.mockResolvedValue(mockResult);

			const result = await controller.findAll(2, 5, 'ASC');

			expect(logger.log).toHaveBeenCalledWith('Fetching all brands', {
				page: 2,
				limit: 5,
				orderBy: 'ASC'
			});
			expect(brandService.getAllBrands).toHaveBeenCalledWith(2, 5, '', 'ASC');
			expect(result).toEqual(mockResult);
		});

		it('should handle service errors and throw HttpException', async () => {
			mockBrandService.getAllBrands.mockRejectedValue(new Error('Service error'));

			await expect(controller.findAll()).rejects.toThrow(HttpException);
			await expect(controller.findAll()).rejects.toThrow('Error fetching all the brands');

			expect(logger.error).toHaveBeenCalledWith('Error fetching brands', {
				error: expect.any(Error)
			});
		});
	});

	describe('findBySlug', () => {
		it('should return brand with settings by slug', async () => {
			const slug = 'testbrand';
			mockBrandService.getBrandBySlug.mockResolvedValue(mockBrandWithSettings);

			const result = await controller.findBySlug(slug);

			expect(brandService.getBrandBySlug).toHaveBeenCalledWith(slug);
			expect(result).toEqual(mockBrandWithSettings);
		});

		it('should return null when brand not found by slug', async () => {
			const slug = 'nonexistent';
			mockBrandService.getBrandBySlug.mockResolvedValue(null);

			const result = await controller.findBySlug(slug);

			expect(brandService.getBrandBySlug).toHaveBeenCalledWith(slug);
			expect(result).toBeNull();
		});

		it('should handle service errors', async () => {
			const slug = 'testbrand';
			mockBrandService.getBrandBySlug.mockRejectedValue(new Error('Service error'));

			await expect(controller.findBySlug(slug)).rejects.toThrow('Service error');
			expect(brandService.getBrandBySlug).toHaveBeenCalledWith(slug);
		});
	});
});
