import { Test, TestingModule } from '@nestjs/testing';
import { BrandService } from './brands.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { Brand } from './entities/brand.entity';
import { CreateBrandDto } from './dto/create-brand.dto';
import { BrandWithSettingsDto } from './dto/brand-with-settings.dto';
import {
	ConflictException,
	InternalServerErrorException,
	BadRequestException
} from '@nestjs/common';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { User } from '../users/entities/user.entity';
import { ClinicEntity } from '../clinics/entities/clinic.entity';

describe('BrandService', () => {
	let service: BrandService;
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	let brandRepository: jest.Mocked<Repository<Brand>>;

	const mockBrandRepository = {
		findOne: jest.fn(),
		save: jest.fn(),
		find: jest.fn(),
		findAndCount: jest.fn(),
		create: jest.fn()
	};

	const mockLogger = {
		log: jest.fn(),
		error: jest.fn()
	};

	const mockBrand: Partial<Brand> = {
		id: '1',
		name: 'Test Brand',
		slug: 'testbrand',
		createdAt: new Date('2023-01-01'),
		updatedAt: new Date('2023-01-01'),
		createdBy: 'user-1',
		updatedBy: 'user-1',
		clinics: []
	};

	const mockClinic = {
		id: 'clinic-1',
		name: 'Test Clinic',
		phoneNumbers: [{ country_code: '+1', number: '**********' }],
		customRule: {
			clientBookingSettings: {
				isEnabled: true
			}
		}
	};

	const mockBrandWithClinics: Partial<Brand> = {
		...mockBrand,
		clinics: [mockClinic as any]
	};

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				BrandService,
				{
					provide: getRepositoryToken(Brand),
					useValue: mockBrandRepository
				},
				{
					provide: WinstonLogger,
					useValue: mockLogger
				}
			]
		}).compile();

		service = module.get<BrandService>(BrandService);
		brandRepository = module.get(getRepositoryToken(Brand));
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	describe('createBrand', () => {
		it('should create a new brand successfully', async () => {
			const createBrandDto: CreateBrandDto = { name: 'New Brand' };

			mockBrandRepository.findOne.mockResolvedValue(null); // No brand exists with the same name
			mockBrandRepository.create.mockReturnValue(mockBrand);
			mockBrandRepository.save.mockResolvedValue(mockBrand);

			const result = await service.createBrand(createBrandDto);

			expect(mockBrandRepository.findOne).toHaveBeenCalledWith({
				where: { name: createBrandDto.name }
			});
			expect(mockBrandRepository.create).toHaveBeenCalledWith(
				createBrandDto
			);
			expect(mockBrandRepository.save).toHaveBeenCalledWith(mockBrand);
			expect(result).toEqual(mockBrand);
		});

		it('should throw ConflictException if a brand with the same name already exists', async () => {
			const createBrandDto: CreateBrandDto = { name: 'Existing Brand' };

			mockBrandRepository.findOne.mockResolvedValue(mockBrand); // Brand already exists

			await expect(service.createBrand(createBrandDto)).rejects.toThrow(
				ConflictException
			);
			expect(mockBrandRepository.findOne).toHaveBeenCalledWith({
				where: { name: createBrandDto.name }
			});
			expect(mockBrandRepository.create).not.toHaveBeenCalled();
			expect(mockBrandRepository.save).not.toHaveBeenCalled();
		});

		it('should throw InternalServerErrorException if an unexpected error occurs', async () => {
			const createBrandDto: CreateBrandDto = { name: 'Error Brand' };

			mockBrandRepository.findOne.mockRejectedValue(
				new InternalServerErrorException()
			); // Simulating an unexpected error

			await expect(service.createBrand(createBrandDto)).rejects.toThrow(
				InternalServerErrorException
			);
			expect(mockBrandRepository.findOne).toHaveBeenCalledWith({
				where: { name: createBrandDto.name }
			});
			expect(mockBrandRepository.create).not.toHaveBeenCalled();
			expect(mockBrandRepository.save).not.toHaveBeenCalled();
		});
	});

	describe('getAllBrands', () => {
		it('should return paginated brands with default parameters', async () => {
			const mockResult = { brands: [mockBrand], total: 1 };
			mockBrandRepository.findAndCount.mockResolvedValue([[mockBrand], 1]);

			const result = await service.getAllBrands();

			expect(mockBrandRepository.findAndCount).toHaveBeenCalledWith({
				where: {},
				skip: 0,
				take: 10,
				order: { createdAt: 'DESC' }
			});
			expect(result).toEqual(mockResult);
		});

		it('should return paginated brands with custom parameters', async () => {
			const mockResult = { brands: [mockBrand], total: 1 };
			mockBrandRepository.findAndCount.mockResolvedValue([[mockBrand], 1]);

			const result = await service.getAllBrands(2, 5, 'test', 'ASC');

			expect(mockBrandRepository.findAndCount).toHaveBeenCalledWith({
				where: { name: Like('%test%') },
				skip: 5,
				take: 5,
				order: { createdAt: 'ASC' }
			});
			expect(result).toEqual(mockResult);
		});

		it('should handle search parameter correctly', async () => {
			const mockResult = { brands: [mockBrand], total: 1 };
			mockBrandRepository.findAndCount.mockResolvedValue([[mockBrand], 1]);

			await service.getAllBrands(1, 10, 'Brand Name');

			expect(mockBrandRepository.findAndCount).toHaveBeenCalledWith({
				where: { name: Like('%Brand Name%') },
				skip: 0,
				take: 10,
				order: { createdAt: 'DESC' }
			});
		});

		it('should throw InternalServerErrorException if an error occurs', async () => {
			mockBrandRepository.findAndCount.mockRejectedValue(
				new Error('Database error')
			);

			await expect(service.getAllBrands()).rejects.toThrow(
				InternalServerErrorException
			);
			expect(mockBrandRepository.findAndCount).toHaveBeenCalled();
		});
	});

	describe('getBrandById', () => {
		it('should return a brand by id', async () => {
			const id = '1';
			mockBrandRepository.findOne.mockResolvedValue(mockBrand);

			const result = await service.getBrandById(id);

			expect(mockBrandRepository.findOne).toHaveBeenCalledWith({
				where: { id }
			});
			expect(result).toEqual(mockBrand);
		});

		it('should return null if brand is not found', async () => {
			const id = '2';
			mockBrandRepository.findOne.mockResolvedValue(null);

			const result = await service.getBrandById(id);

			expect(mockBrandRepository.findOne).toHaveBeenCalledWith({
				where: { id }
			});
			expect(result).toBeNull();
		});

		it('should throw InternalServerErrorException if an error occurs', async () => {
			const id = '3';
			mockBrandRepository.findOne.mockRejectedValue(
				new InternalServerErrorException()
			);

			await expect(service.getBrandById(id)).rejects.toThrow(
				InternalServerErrorException
			);
			expect(mockBrandRepository.findOne).toHaveBeenCalledWith({
				where: { id }
			});
		});
	});

	describe('getAllBrandsSimple', () => {
		it('should return all brands without pagination', async () => {
			mockBrandRepository.find.mockResolvedValue([mockBrand]);

			const result = await service.getAllBrandsSimple();

			expect(mockBrandRepository.find).toHaveBeenCalledWith({
				order: { createdAt: 'DESC' }
			});
			expect(result).toEqual([mockBrand]);
		});

		it('should throw InternalServerErrorException if an error occurs', async () => {
			mockBrandRepository.find.mockRejectedValue(
				new Error('Database error')
			);

			await expect(service.getAllBrandsSimple()).rejects.toThrow(
				InternalServerErrorException
			);
			expect(mockBrandRepository.find).toHaveBeenCalled();
		});
	});

	describe('getBrandBySlug', () => {
		it('should return brand with settings when brand exists with client booking enabled', async () => {
			mockBrandRepository.findOne.mockResolvedValue(mockBrandWithClinics);

			const result = await service.getBrandBySlug('testbrand');

			expect(mockBrandRepository.findOne).toHaveBeenCalledWith({
				where: { slug: 'testbrand' },
				relations: ['clinics']
			});
			expect(result).toBeInstanceOf(BrandWithSettingsDto);
			expect(result?.hasClientBookingEnabled).toBe(true);
			expect(result?.clinics).toHaveLength(1);
			expect(result?.clinics?.[0].phoneNumbers).toEqual([
				{ country_code: '+1', number: '**********' }
			]);
		});

		it('should return brand with settings when brand exists with client booking disabled', async () => {
			const mockBrandWithDisabledBooking = {
				...mockBrandWithClinics,
				clinics: [{
					...mockClinic,
					customRule: {
						clientBookingSettings: {
							isEnabled: false
						}
					}
				}]
			};
			mockBrandRepository.findOne.mockResolvedValue(mockBrandWithDisabledBooking);

			const result = await service.getBrandBySlug('testbrand');

			expect(result?.hasClientBookingEnabled).toBe(false);
		});

		it('should return brand with settings when brand has no clinics', async () => {
			const mockBrandNoClinics = {
				...mockBrand,
				clinics: []
			};
			mockBrandRepository.findOne.mockResolvedValue(mockBrandNoClinics);

			const result = await service.getBrandBySlug('testbrand');

			expect(result?.hasClientBookingEnabled).toBe(false);
			expect(result?.clinics).toBeUndefined();
		});

		it('should return null when brand does not exist', async () => {
			mockBrandRepository.findOne.mockResolvedValue(null);

			const result = await service.getBrandBySlug('nonexistent');

			expect(mockBrandRepository.findOne).toHaveBeenCalledWith({
				where: { slug: 'nonexistent' },
				relations: ['clinics']
			});
			expect(result).toBeNull();
		});

		it('should throw InternalServerErrorException if an error occurs', async () => {
			mockBrandRepository.findOne.mockRejectedValue(
				new Error('Database error')
			);

			await expect(service.getBrandBySlug('testbrand')).rejects.toThrow(
				InternalServerErrorException
			);
			expect(mockBrandRepository.findOne).toHaveBeenCalledWith({
				where: { slug: 'testbrand' },
				relations: ['clinics']
			});
		});
	});
});
