"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmrModule = void 0;
const common_1 = require("@nestjs/common");
const emr_controller_1 = require("./emr.controller");
const emr_service_1 = require("./emr.service");
const typeorm_1 = require("@nestjs/typeorm");
const emr_entity_1 = require("./entities/emr.entity");
const appointment_details_entity_1 = require("../appointments/entities/appointment-details.entity");
const s3_service_1 = require("../utils/aws/s3/s3.service");
const ses_module_1 = require("../utils/aws/ses/ses.module");
const whatsapp_module_1 = require("../utils/whatsapp-integration/whatsapp.module");
const send_document_service_1 = require("../utils/common/send-document.service");
const appointments_module_1 = require("../appointments/appointments.module");
const appointment_entity_1 = require("../appointments/entities/appointment.entity");
const patient_vaccinations_entity_1 = require("../patient-vaccinations/entities/patient-vaccinations.entity");
const patient_entity_1 = require("../patients/entities/patient.entity");
const payment_details_entity_1 = require("../payment-details/entities/payment-details.entity");
const sqs_service_1 = require("../utils/aws/sqs/sqs.service");
const sqs_module_1 = require("../utils/aws/sqs/sqs.module");
const lab_report_entity_1 = require("../clinic-lab-report/entities/lab-report.entity");
const invoice_entity_1 = require("../invoice/entities/invoice.entity");
const appointment_doctor_entity_1 = require("../appointments/entities/appointment-doctor.entity");
const tab_activity_module_1 = require("../tab-activity/tab-activity.module");
const invoice_module_1 = require("../invoice/invoice.module");
const merged_invoice_document_entity_1 = require("../invoice/entities/merged-invoice-document.entity");
const merged_payment_receipt_document_entity_1 = require("../payment-details/entities/merged-payment-receipt-document.entity");
let EmrModule = class EmrModule {
};
exports.EmrModule = EmrModule;
exports.EmrModule = EmrModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                emr_entity_1.Emr,
                appointment_details_entity_1.AppointmentDetailsEntity,
                appointment_entity_1.AppointmentEntity,
                patient_vaccinations_entity_1.PatientVaccination,
                patient_entity_1.Patient,
                payment_details_entity_1.PaymentDetailsEntity,
                lab_report_entity_1.LabReport,
                invoice_entity_1.InvoiceEntity,
                appointment_doctor_entity_1.AppointmentDoctorsEntity,
                merged_invoice_document_entity_1.MergedInvoiceDocumentEntity,
                merged_payment_receipt_document_entity_1.MergedPaymentReceiptDocumentEntity
            ]),
            ses_module_1.SESModule,
            whatsapp_module_1.WhatsappModule,
            appointments_module_1.AppointmentsModule,
            tab_activity_module_1.TabActivityModule,
            (0, common_1.forwardRef)(() => invoice_module_1.InvoiceModule),
            (0, common_1.forwardRef)(() => sqs_module_1.SqsModule)
        ],
        controllers: [emr_controller_1.EmrController],
        providers: [emr_service_1.EmrService, s3_service_1.S3Service, send_document_service_1.SendDocuments, sqs_service_1.SqsService],
        exports: [emr_service_1.EmrService, typeorm_1.TypeOrmModule]
    })
], EmrModule);
//# sourceMappingURL=emr.module.js.map