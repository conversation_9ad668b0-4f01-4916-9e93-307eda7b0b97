"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoiceModule = void 0;
const common_1 = require("@nestjs/common");
const invoice_service_1 = require("./invoice.service");
const invoice_controller_1 = require("./invoice.controller");
const invoice_entity_1 = require("./entities/invoice.entity");
const typeorm_1 = require("@nestjs/typeorm");
const patients_service_1 = require("../patients/patients.service");
const patient_entity_1 = require("../patients/entities/patient.entity");
const patient_owner_entity_1 = require("../patients/entities/patient-owner.entity");
const clinic_entity_1 = require("../clinics/entities/clinic.entity");
const owners_service_1 = require("../owners/owners.service");
const s3_service_1 = require("../utils/aws/s3/s3.service");
const patient_vaccinations_service_1 = require("../patient-vaccinations/patient-vaccinations.service");
const patient_vaccinations_entity_1 = require("../patient-vaccinations/entities/patient-vaccinations.entity");
const ses_module_1 = require("../utils/aws/ses/ses.module");
const clinic_product_entity_1 = require("../clinic-products/entities/clinic-product.entity");
const clinic_vaccination_entity_1 = require("../clinic-vaccinations/entities/clinic-vaccination.entity");
const clinic_medication_entity_1 = require("../clinic-medications/entities/clinic-medication.entity");
const clinic_plan_entity_1 = require("../clinic-plans/entities/clinic-plan.entity");
const clinic_consumable_entity_1 = require("../clinic-consumables/entities/clinic-consumable.entity");
const carts_service_1 = require("../carts/carts.service");
const cart_entity_1 = require("../carts/entites/cart.entity");
const appointment_doctor_entity_1 = require("../appointments/entities/appointment-doctor.entity");
const whatsapp_module_1 = require("../utils/whatsapp-integration/whatsapp.module");
const cart_item_entity_1 = require("../cart-items/entities/cart-item.entity");
const patient_reminder_module_1 = require("../patient-reminders/patient-reminder.module");
const global_reminders_service_1 = require("../patient-global-reminders/global-reminders.service");
const global_reminder_rule_entity_1 = require("../patient-global-reminders/entities/global-reminder-rule.entity");
const patient_reminder_entity_1 = require("../patient-reminders/entities/patient-reminder.entity");
const global_owner_entity_1 = require("../owners/entities/global-owner.entity");
const owner_brand_entity_1 = require("../owners/entities/owner-brand.entity");
const owners_module_1 = require("../owners/owners.module");
const role_module_1 = require("../roles/role.module");
const appointment_details_entity_1 = require("../appointments/entities/appointment-details.entity");
const brand_entity_1 = require("../brands/entities/brand.entity");
const pet_transfer_history_entity_1 = require("../owners/entities/pet-transfer-history.entity");
const payment_details_entity_1 = require("../payment-details/entities/payment-details.entity");
const credits_module_1 = require("../credits/credits.module");
const sqs_module_1 = require("../utils/aws/sqs/sqs.module");
const tab_activity_module_1 = require("../tab-activity/tab-activity.module");
const merged_invoice_document_entity_1 = require("./entities/merged-invoice-document.entity");
const invoice_audit_log_entity_1 = require("./entities/invoice-audit-log.entity");
const appointments_module_1 = require("../appointments/appointments.module");
const sqs_service_1 = require("../utils/aws/sqs/sqs.service");
const availability_module_1 = require("../availability/availability.module");
const appointment_entity_1 = require("../appointments/entities/appointment.entity");
const payment_details_module_1 = require("../payment-details/payment-details.module");
const clinic_lab_report_service_1 = require("../clinic-lab-report/clinic-lab-report.service");
const lab_report_entity_1 = require("../clinic-lab-report/entities/lab-report.entity");
const clinic_lab_report_entity_1 = require("../clinic-lab-report/entities/clinic-lab-report.entity");
const socket_module_1 = require("../socket/socket.module");
let InvoiceModule = class InvoiceModule {
};
exports.InvoiceModule = InvoiceModule;
exports.InvoiceModule = InvoiceModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                invoice_entity_1.InvoiceEntity,
                patient_entity_1.Patient,
                patient_owner_entity_1.PatientOwner,
                clinic_entity_1.ClinicEntity,
                patient_vaccinations_entity_1.PatientVaccination,
                clinic_product_entity_1.ClinicProductEntity,
                clinic_vaccination_entity_1.ClinicVaccinationEntity,
                clinic_medication_entity_1.ClinicMedicationEntity,
                clinic_plan_entity_1.ClinicPlan,
                clinic_consumable_entity_1.ClinicConsumableEntity,
                cart_entity_1.CartEntity,
                cart_item_entity_1.CartItemEntity,
                appointment_doctor_entity_1.AppointmentDoctorsEntity,
                global_reminder_rule_entity_1.GlobalReminderRule,
                patient_reminder_entity_1.PatientReminder,
                patient_vaccinations_entity_1.PatientVaccination,
                global_owner_entity_1.GlobalOwner,
                owner_brand_entity_1.OwnerBrand,
                appointment_details_entity_1.AppointmentDetailsEntity,
                brand_entity_1.Brand,
                pet_transfer_history_entity_1.PetTransferHistory,
                payment_details_entity_1.PaymentDetailsEntity,
                merged_invoice_document_entity_1.MergedInvoiceDocumentEntity,
                invoice_audit_log_entity_1.InvoiceAuditLogEntity,
                appointment_entity_1.AppointmentEntity,
                lab_report_entity_1.LabReport,
                clinic_lab_report_entity_1.ClinicLabReport
            ]),
            role_module_1.RoleModule,
            ses_module_1.SESModule,
            whatsapp_module_1.WhatsappModule,
            patient_reminder_module_1.PatientRemindersModule,
            owners_module_1.OwnersModule,
            credits_module_1.CreditsModule,
            (0, common_1.forwardRef)(() => sqs_module_1.SqsModule),
            tab_activity_module_1.TabActivityModule,
            (0, common_1.forwardRef)(() => appointments_module_1.AppointmentsModule),
            (0, common_1.forwardRef)(() => availability_module_1.AvailabilityModule),
            (0, common_1.forwardRef)(() => payment_details_module_1.PaymentDetailsModule),
            (0, common_1.forwardRef)(() => socket_module_1.SocketModule)
        ],
        controllers: [invoice_controller_1.InvoiceController],
        providers: [
            invoice_service_1.InvoiceService,
            patients_service_1.PatientsService,
            owners_service_1.OwnersService,
            s3_service_1.S3Service,
            patient_vaccinations_service_1.PatientVaccinationsService,
            carts_service_1.CartsService,
            global_reminders_service_1.GlobalReminderService,
            sqs_service_1.SqsService,
            clinic_lab_report_service_1.ClinicLabReportService
        ],
        exports: [invoice_service_1.InvoiceService]
    })
], InvoiceModule);
//# sourceMappingURL=invoice.module.js.map